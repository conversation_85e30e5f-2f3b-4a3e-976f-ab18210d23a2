<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sync Checkbox</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
            color: #333;
            cursor: pointer;
            margin: 10px 0;
        }
        .setting-note {
            font-size: 0.75rem;
            color: #666;
            opacity: 0.7;
            margin-top: 5px;
            margin-left: 28px;
            font-style: italic;
        }
        .language-selector {
            margin: 20px 0;
        }
        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .test-result {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .error {
            background-color: #ffeaea;
            border-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Sync Checkbox Translation Test</h1>
        
        <div class="language-selector">
            <label>Select Language:</label>
            <select id="language-select">
                <option value="en">English</option>
                <option value="ar">Arabic</option>
                <option value="ckb">Kurdish (Sorani)</option>
            </select>
        </div>

        <div class="sync-auto-option">
            <label class="checkbox-label">
                <input type="checkbox" id="sync-auto-checkbox" />
                <span data-i18n="sync.autoSync">Auto Sync</span>
            </label>
            <div class="setting-note" data-i18n="sync.autoSyncNote">
                Automatically sync your times when connected to the internet
            </div>
        </div>

        <div id="test-result" class="test-result" style="display: none;"></div>
    </div>

    <script type="module">
        // Import language modules
        const languageModules = {
            en: null,
            ar: null,
            ckb: null
        };

        // Load language modules
        async function loadLanguageModule(lang) {
            try {
                const module = await import(`./assets/js/lang/${lang}.js`);
                languageModules[lang] = module.default;
                return module.default;
            } catch (error) {
                console.error(`Failed to load language ${lang}:`, error);
                return null;
            }
        }

        // Apply translations
        function applyTranslations(translations) {
            const elementsWithI18n = document.querySelectorAll('[data-i18n]');
            elementsWithI18n.forEach(element => {
                const key = element.getAttribute('data-i18n');
                const translation = getNestedTranslation(translations, key);
                if (translation) {
                    element.textContent = translation;
                }
            });

            // Set document direction
            document.documentElement.setAttribute('dir', translations.dir || 'ltr');
        }

        // Get nested translation
        function getNestedTranslation(obj, path) {
            return path.split('.').reduce((current, key) => current && current[key], obj);
        }

        // Test function
        async function testLanguage(lang) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result';
            
            try {
                const translations = await loadLanguageModule(lang);
                if (!translations) {
                    throw new Error(`Failed to load ${lang} translations`);
                }

                // Check if sync translations exist
                if (!translations.sync) {
                    throw new Error(`Missing sync section in ${lang} translations`);
                }

                if (!translations.sync.autoSync) {
                    throw new Error(`Missing autoSync translation in ${lang}`);
                }

                if (!translations.sync.autoSyncNote) {
                    throw new Error(`Missing autoSyncNote translation in ${lang}`);
                }

                // Apply translations
                applyTranslations(translations);

                resultDiv.innerHTML = `
                    <strong>✅ Success!</strong><br>
                    Language: ${lang}<br>
                    autoSync: "${translations.sync.autoSync}"<br>
                    autoSyncNote: "${translations.sync.autoSyncNote}"<br>
                    Direction: ${translations.dir || 'ltr'}
                `;
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>❌ Error:</strong> ${error.message}`;
            }
        }

        // Language selector event
        document.getElementById('language-select').addEventListener('change', (e) => {
            testLanguage(e.target.value);
        });

        // Test English by default
        testLanguage('en');
    </script>
</body>
</html>
